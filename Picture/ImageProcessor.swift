//
//  ImageProcessor.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

#if canImport(UIKit)
import UIKit
typealias PlatformImage = UIImage
typealias PlatformColor = UIColor
#elseif canImport(AppKit)
import AppKit
typealias PlatformImage = NSImage
typealias PlatformColor = NSColor
#endif
import CoreImage
import Vision
import CoreML

class ImageProcessor: ObservableObject {
    @Published var isProcessing = false
    @Published var processedImage: PlatformImage?
    @Published var errorMessage: String?
    
    private let context = CIContext()
    
    // MARK: - Background Removal using Vision
    func removeBackground(from image: PlatformImage, completion: @escaping (PlatformImage?) -> Void) {
        isProcessing = true
        errorMessage = nil
        
        guard let cgImage = image.cgImage else {
            DispatchQueue.main.async {
                self.isProcessing = false
                self.errorMessage = "无法处理图片"
                completion(nil)
            }
            return
        }
        
        // Create Vision request for person segmentation
        let request = VNGeneratePersonSegmentationRequest { [weak self] request, error in
            DispatchQueue.main.async {
                self?.isProcessing = false
                
                if let error = error {
                    self?.errorMessage = "处理失败: \(error.localizedDescription)"
                    completion(nil)
                    return
                }
                
                guard let observation = request.results?.first as? VNPixelBufferObservation else {
                    self?.errorMessage = "无法生成遮罩"
                    completion(nil)
                    return
                }
                
                let maskedImage = self?.applyMask(to: image, mask: observation.pixelBuffer)
                self?.processedImage = maskedImage
                completion(maskedImage)
            }
        }
        
        request.qualityLevel = .accurate
        request.outputPixelFormat = kCVPixelFormatType_OneComponent8
        
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                try handler.perform([request])
            } catch {
                DispatchQueue.main.async {
                    self.isProcessing = false
                    self.errorMessage = "处理失败: \(error.localizedDescription)"
                    completion(nil)
                }
            }
        }
    }
    
    // MARK: - Apply mask to image
    private func applyMask(to image: PlatformImage, mask: CVPixelBuffer) -> PlatformImage? {
        guard let cgImage = image.cgImage else { return nil }
        
        let ciImage = CIImage(cgImage: cgImage)
        let maskImage = CIImage(cvPixelBuffer: mask)
        
        // Scale mask to match image size
        let scaleX = ciImage.extent.width / maskImage.extent.width
        let scaleY = ciImage.extent.height / maskImage.extent.height
        let scaledMask = maskImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))
        
        // Apply mask using blend filter
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else { return nil }
        blendFilter.setValue(ciImage, forKey: kCIInputImageKey)
        blendFilter.setValue(CIImage.clear, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(scaledMask, forKey: kCIInputMaskImageKey)
        
        guard let outputImage = blendFilter.outputImage,
              let cgOutput = context.createCGImage(outputImage, from: outputImage.extent) else {
            return nil
        }
        
        return PlatformImage(cgImage: cgOutput)
    }
    
    // MARK: - Manual cutout with touch points
    func createMaskFromPath(_ path: [CGPoint], imageSize: CGSize) -> PlatformImage? {
        #if canImport(UIKit)
        let renderer = UIGraphicsImageRenderer(size: imageSize)

        return renderer.image { context in
            // Fill with black (transparent areas)
            context.cgContext.setFillColor(PlatformColor.black.cgColor)
            context.cgContext.fill(CGRect(origin: .zero, size: imageSize))

            // Draw white path (visible areas)
            context.cgContext.setFillColor(PlatformColor.white.cgColor)
            context.cgContext.setStrokeColor(PlatformColor.white.cgColor)
            context.cgContext.setLineWidth(20)
            context.cgContext.setLineCap(.round)
            context.cgContext.setLineJoin(.round)

            if path.count > 1 {
                context.cgContext.move(to: path[0])
                for point in path.dropFirst() {
                    context.cgContext.addLine(to: point)
                }
                context.cgContext.closePath()
                context.cgContext.fillPath()
            }
        }
        #elseif canImport(AppKit)
        let image = NSImage(size: imageSize)
        image.lockFocus()

        // Fill with black (transparent areas)
        PlatformColor.black.setFill()
        NSRect(origin: .zero, size: imageSize).fill()

        // Draw white path (visible areas)
        PlatformColor.white.setFill()
        PlatformColor.white.setStroke()

        let bezierPath = NSBezierPath()
        bezierPath.lineWidth = 20
        bezierPath.lineCapStyle = .round
        bezierPath.lineJoinStyle = .round

        if path.count > 1 {
            bezierPath.move(to: path[0])
            for point in path.dropFirst() {
                bezierPath.line(to: point)
            }
            bezierPath.close()
            bezierPath.fill()
        }

        image.unlockFocus()
        return image
        #endif
    }
    
    // MARK: - Apply custom mask
    func applyCustomMask(to image: PlatformImage, mask: PlatformImage) -> PlatformImage? {
        guard let cgImage = image.cgImage,
              let maskCGImage = mask.cgImage else { return nil }
        
        let ciImage = CIImage(cgImage: cgImage)
        let maskImage = CIImage(cgImage: maskCGImage)
        
        guard let blendFilter = CIFilter(name: "CIBlendWithMask") else { return nil }
        blendFilter.setValue(ciImage, forKey: kCIInputImageKey)
        blendFilter.setValue(CIImage.clear, forKey: kCIInputBackgroundImageKey)
        blendFilter.setValue(maskImage, forKey: kCIInputMaskImageKey)
        
        guard let outputImage = blendFilter.outputImage,
              let cgOutput = context.createCGImage(outputImage, from: outputImage.extent) else {
            return nil
        }
        
        return PlatformImage(cgImage: cgOutput)
    }
    
    // MARK: - Add background color
    func addBackground(to image: PlatformImage, color: PlatformColor) -> PlatformImage? {
        #if canImport(UIKit)
        let renderer = UIGraphicsImageRenderer(size: image.size)

        return renderer.image { context in
            // Fill background with color
            context.cgContext.setFillColor(color.cgColor)
            context.cgContext.fill(CGRect(origin: .zero, size: image.size))

            // Draw image on top
            image.draw(at: .zero)
        }
        #elseif canImport(AppKit)
        let newImage = NSImage(size: image.size)
        newImage.lockFocus()

        // Fill background with color
        color.setFill()
        NSRect(origin: .zero, size: image.size).fill()

        // Draw image on top
        image.draw(at: .zero, from: NSRect(origin: .zero, size: image.size), operation: .sourceOver, fraction: 1.0)

        newImage.unlockFocus()
        return newImage
        #endif
    }
    
    // MARK: - Edge refinement
    func refineEdges(of image: PlatformImage) -> PlatformImage? {
        guard let cgImage = image.cgImage else { return nil }
        
        let ciImage = CIImage(cgImage: cgImage)
        
        // Apply edge enhancement filter
        guard let filter = CIFilter(name: "CIUnsharpMask") else { return nil }
        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(0.5, forKey: kCIInputIntensityKey)
        filter.setValue(2.0, forKey: kCIInputRadiusKey)
        
        guard let outputImage = filter.outputImage,
              let cgOutput = context.createCGImage(outputImage, from: outputImage.extent) else {
            return nil
        }
        
        return PlatformImage(cgImage: cgOutput)
    }
}

// MARK: - Platform-specific extensions
#if canImport(UIKit)
extension UIImage {
    var cgImage: CGImage? {
        return self.cgImage
    }

    convenience init?(cgImage: CGImage) {
        self.init(cgImage: cgImage)
    }
}
#elseif canImport(AppKit)
extension NSImage {
    var cgImage: CGImage? {
        var proposedRect = CGRect(origin: .zero, size: size)
        return cgImage(forProposedRect: &proposedRect, context: nil, hints: nil)
    }

    convenience init?(cgImage: CGImage) {
        self.init(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
    }
}
#endif
