//
//  PlatformTypes.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import Foundation
import SwiftUI

// Platform-specific imports and type aliases
#if canImport(UIKit)
import UIKit
public typealias PlatformImage = UIImage
public typealias PlatformColor = UIColor
public typealias PlatformView = UIView
#elseif canImport(AppKit)
import AppKit
public typealias PlatformImage = NSImage
public typealias PlatformColor = NSColor
public typealias PlatformView = NSView
#endif

// Platform-specific extensions and utilities
#if canImport(UIKit)
extension UIColor {
    convenience init(_ color: SwiftUI.Color) {
        let uiColor = UIColor(color)
        self.init(cgColor: uiColor.cgColor)
    }
}

extension UIImage {
    var cgImage: CGImage? {
        return self.cgImage
    }

    convenience init?(cgImage: CGImage) {
        self.init(cgImage: cgImage)
    }
}
#elseif canImport(AppKit)
extension NSColor {
    convenience init(_ color: SwiftUI.Color) {
        let nsColor = NSColor(color)
        self.init(cgColor: nsColor.cgColor ?? CGColor(red: 0, green: 0, blue: 0, alpha: 1))!
    }
}

extension NSImage {
    var cgImage: CGImage? {
        var proposedRect = CGRect(origin: .zero, size: size)
        return cgImage(forProposedRect: &proposedRect, context: nil, hints: nil)
    }

    convenience init?(cgImage: CGImage) {
        self.init(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
    }
}
#endif

// Platform-specific SwiftUI utilities
#if canImport(UIKit)
extension View {
    var screenBounds: CGRect {
        UIScreen.main.bounds
    }
}
#elseif canImport(AppKit)
extension View {
    var screenBounds: CGRect {
        NSScreen.main?.frame ?? CGRect(x: 0, y: 0, width: 800, height: 600)
    }
}
#endif
