//
//  HelpView.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import SwiftUI

struct HelpView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 25) {
                    // Header
                    VStack(spacing: 10) {
                        Image(systemName: "questionmark.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)
                        
                        Text("使用帮助")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("学习如何使用智能抠图工具")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.bottom, 20)
                    
                    // Tutorial Steps
                    VStack(alignment: .leading, spacing: 20) {
                        TutorialStep(
                            number: 1,
                            title: "选择图片",
                            description: "从相册选择图片或使用相机拍摄新照片",
                            icon: "photo.on.rectangle"
                        )
                        
                        TutorialStep(
                            number: 2,
                            title: "选择抠图模式",
                            description: "使用AI自动抠图或手动绘制选区进行精确控制",
                            icon: "wand.and.stars"
                        )
                        
                        TutorialStep(
                            number: 3,
                            title: "调整和优化",
                            description: "使用手动工具精修边缘，添加背景颜色或图片",
                            icon: "slider.horizontal.3"
                        )
                        
                        TutorialStep(
                            number: 4,
                            title: "保存和分享",
                            description: "将处理后的图片保存到相册或分享给朋友",
                            icon: "square.and.arrow.up"
                        )
                    }
                    
                    Divider()
                        .padding(.vertical)
                    
                    // Tips Section
                    VStack(alignment: .leading, spacing: 15) {
                        Text("使用技巧")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        TipItem(
                            icon: "lightbulb",
                            tip: "选择对比度高的图片可以获得更好的自动抠图效果"
                        )
                        
                        TipItem(
                            icon: "hand.draw",
                            tip: "使用手动模式时，尽量绘制完整的闭合区域"
                        )
                        
                        TipItem(
                            icon: "paintbrush",
                            tip: "添加背景时可以选择与主体颜色对比明显的颜色"
                        )
                        
                        TipItem(
                            icon: "photo",
                            tip: "处理人像时，AI模式通常能提供最佳效果"
                        )
                    }
                    
                    Spacer(minLength: 50)
                }
                .padding()
            }
            .navigationTitle("帮助")
            #if canImport(UIKit)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
            #else
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            #endif
        }
    }
}

// MARK: - Supporting Views
struct TutorialStep: View {
    let number: Int
    let title: String
    let description: String
    let icon: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            // Step Number
            ZStack {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 30, height: 30)
                
                Text("\(number)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(.blue)
                    
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

struct TipItem: View {
    let icon: String
    let tip: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.orange)
                .frame(width: 20)
            
            Text(tip)
                .font(.body)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
            
            Spacer()
        }
    }
}

// MARK: - Preview
struct HelpView_Previews: PreviewProvider {
    static var previews: some View {
        HelpView()
    }
}
