//
//  EditingView.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import SwiftUI

#if canImport(UIKit)
import UIKit
typealias PlatformImage = UIImage
#elseif canImport(AppKit)
import AppKit
typealias PlatformImage = NSImage
#endif

struct EditingView: View {
    @StateObject private var imageProcessor = ImageProcessor()
    @State private var originalImage: PlatformImage
    @State private var showingBackgroundPicker = false
    @State private var selectedBackgroundColor = Color.white
    @State private var editingMode: EditingMode = .auto
    @State private var drawingPath: [CGPoint] = []
    @State private var isDrawing = false
    @Environment(\.presentationMode) var presentationMode
    
    enum EditingMode {
        case auto, manual, background
    }
    
    init(image: PlatformImage) {
        self._originalImage = State(initialValue: image)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Image Preview Area
                GeometryReader { geometry in
                    ZStack {
                        Color.gray.opacity(0.1)
                        
                        if let processedImage = imageProcessor.processedImage {
                            #if canImport(UIKit)
                            Image(uiImage: processedImage)
                            #elseif canImport(AppKit)
                            Image(nsImage: processedImage)
                            #endif
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxWidth: geometry.size.width, maxHeight: geometry.size.height)
                        } else {
                            #if canImport(UIKit)
                            Image(uiImage: originalImage)
                            #elseif canImport(AppKit)
                            Image(nsImage: originalImage)
                            #endif
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxWidth: geometry.size.width, maxHeight: geometry.size.height)
                                .overlay(
                                    // Manual editing overlay
                                    editingMode == .manual ? 
                                    ManualEditingOverlay(
                                        drawingPath: $drawingPath,
                                        isDrawing: $isDrawing,
                                        imageSize: originalImage.size
                                    ) : nil
                                )
                        }
                        
                        // Loading indicator
                        if imageProcessor.isProcessing {
                            ProgressView("处理中...")
                                .padding()
                                .background(Color.black.opacity(0.7))
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                    }
                }
                .frame(maxHeight: UIScreen.main.bounds.height * 0.6)
                
                // Tool Bar
                VStack(spacing: 15) {
                    // Mode Selection
                    HStack(spacing: 20) {
                        ModeButton(
                            title: "自动抠图",
                            icon: "wand.and.stars",
                            isSelected: editingMode == .auto
                        ) {
                            editingMode = .auto
                        }
                        
                        ModeButton(
                            title: "手动抠图",
                            icon: "scribble",
                            isSelected: editingMode == .manual
                        ) {
                            editingMode = .manual
                        }
                        
                        ModeButton(
                            title: "背景",
                            icon: "paintbrush",
                            isSelected: editingMode == .background
                        ) {
                            editingMode = .background
                        }
                    }
                    
                    // Action Buttons
                    HStack(spacing: 15) {
                        if editingMode == .auto {
                            Button("开始抠图") {
                                imageProcessor.removeBackground(from: originalImage) { _ in }
                            }
                            .buttonStyle(PrimaryButtonStyle())
                            .disabled(imageProcessor.isProcessing)
                        } else if editingMode == .manual {
                            Button("应用选区") {
                                applyManualSelection()
                            }
                            .buttonStyle(PrimaryButtonStyle())
                            .disabled(drawingPath.isEmpty)
                            
                            Button("清除") {
                                drawingPath.removeAll()
                            }
                            .buttonStyle(SecondaryButtonStyle())
                        } else if editingMode == .background {
                            ColorPicker("背景颜色", selection: $selectedBackgroundColor)
                                .labelsHidden()
                                .frame(width: 50, height: 40)
                            
                            Button("应用背景") {
                                applyBackground()
                            }
                            .buttonStyle(PrimaryButtonStyle())
                        }
                        
                        Button("重置") {
                            resetImage()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }
                    .padding(.horizontal)
                }
                .padding()
                .background(Color(.systemGray6))
            }
            .navigationTitle("图片编辑")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: NavigationLink(
                    destination: SaveShareView(
                        originalImage: originalImage,
                        processedImage: imageProcessor.processedImage ?? originalImage
                    )
                ) {
                    Text("完成")
                        .fontWeight(.semibold)
                }
                .disabled(imageProcessor.processedImage == nil)
            )
        }
        .alert("错误", isPresented: .constant(imageProcessor.errorMessage != nil)) {
            Button("确定") {
                imageProcessor.errorMessage = nil
            }
        } message: {
            Text(imageProcessor.errorMessage ?? "")
        }
    }
    
    private func applyManualSelection() {
        guard !drawingPath.isEmpty else { return }
        
        if let maskImage = imageProcessor.createMaskFromPath(drawingPath, imageSize: originalImage.size),
           let result = imageProcessor.applyCustomMask(to: originalImage, mask: maskImage) {
            imageProcessor.processedImage = result
        }
    }
    
    private func applyBackground() {
        guard let currentImage = imageProcessor.processedImage else { return }
        
        let uiColor = UIColor(selectedBackgroundColor)
        if let result = imageProcessor.addBackground(to: currentImage, color: uiColor) {
            imageProcessor.processedImage = result
        }
    }
    
    private func resetImage() {
        imageProcessor.processedImage = nil
        drawingPath.removeAll()
    }
}

// MARK: - Supporting Views
struct ModeButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 5) {
                Image(systemName: icon)
                    .font(.title2)
                Text(title)
                    .font(.caption)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(isSelected ? Color.blue : Color.clear)
            .cornerRadius(8)
        }
    }
}

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(Color.blue)
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.primary)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(Color(.systemGray5))
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}
