//
//  LaunchScreen.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import SwiftUI

struct LaunchScreen: View {
    @State private var isAnimating = false
    @State private var showMainView = false
    
    var body: some View {
        if showMainView {
            ContentView()
        } else {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.6)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // App Icon Animation
                    Image(systemName: "scissors.badge.ellipsis")
                        .font(.system(size: 100))
                        .foregroundColor(.white)
                        .scaleEffect(isAnimating ? 1.2 : 1.0)
                        .rotationEffect(.degrees(isAnimating ? 360 : 0))
                        .animation(
                            Animation.easeInOut(duration: 2.0)
                                .repeatForever(autoreverses: true),
                            value: isAnimating
                        )
                    
                    // App Name
                    VStack(spacing: 10) {
                        Text("智能抠图")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("AI驱动的专业抠图工具")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .opacity(isAnimating ? 1.0 : 0.0)
                    .animation(.easeIn(duration: 1.0).delay(0.5), value: isAnimating)
                }
            }
            .onAppear {
                isAnimating = true
                
                // Auto transition to main view after 3 seconds
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        showMainView = true
                    }
                }
            }
        }
    }
}

#Preview {
    LaunchScreen()
}
