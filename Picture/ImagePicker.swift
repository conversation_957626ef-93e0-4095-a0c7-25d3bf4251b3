//
//  ImagePicker.swift
//  Picture
//
//  Created by linco<PERSON> on 6/17/25.
//

import SwiftUI
import PhotosUI

#if canImport(UIKit)

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: PlatformImage?
    @Environment(\.presentationMode) var presentationMode

    var sourceType: UIImagePickerController.SourceType

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
#elseif canImport(AppKit)

// On macOS, we'll use a different approach since UIImagePickerController is not available
struct ImagePicker: View {
    @Binding var selectedImage: PlatformImage?
    @Environment(\.presentationMode) var presentationMode

    var sourceType: Int // Placeholder for compatibility

    var body: some View {
        VStack {
            Text("Image picker not available on macOS")
                .foregroundColor(.secondary)
            Button("Use Photos Picker Instead") {
                presentationMode.wrappedValue.dismiss()
            }
        }
        .padding()
    }
}
#endif

// PhotosPicker for iOS 16+ and macOS
struct ModernImagePicker: View {
    @Binding var selectedImage: PlatformImage?
    @State private var selectedItem: PhotosPickerItem?

    var body: some View {
        PhotosPicker(
            selection: $selectedItem,
            matching: .images,
            photoLibrary: .shared()
        ) {
            HStack {
                Image(systemName: "photo.on.rectangle")
                Text("选择图片")
            }
            .foregroundColor(.blue)
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(10)
        }
        .onChange(of: selectedItem) { newItem in
            Task {
                guard let newItem = newItem else { return }
                if let data = try? await newItem.loadTransferable(type: Data.self) {
                    #if canImport(UIKit)
                    if let image = UIImage(data: data) {
                        await MainActor.run {
                            selectedImage = image
                        }
                    }
                    #elseif canImport(AppKit)
                    if let image = NSImage(data: data) {
                        await MainActor.run {
                            selectedImage = image
                        }
                    }
                    #endif
                }
            }
        }
    }
}

// Image source selection view
struct ImageSourcePicker: View {
    @Binding var selectedImage: PlatformImage?
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    @State private var showingModernPicker = false

    #if canImport(UIKit)
    @State private var sourceType: UIImagePickerController.SourceType = .photoLibrary
    #else
    @State private var sourceType: Int = 0
    #endif

    var body: some View {
        VStack(spacing: 20) {
            Text("选择图片来源")
                .font(.title2)
                .fontWeight(.semibold)

            VStack(spacing: 15) {
                // Modern Photos Picker (works on both platforms)
                Button(action: {
                    showingModernPicker = true
                }) {
                    HStack {
                        Image(systemName: "photo.on.rectangle")
                            .font(.title2)
                        Text("从相册选择")
                            .font(.headline)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }

                #if canImport(UIKit)
                // Legacy Photo Library Button (iOS only)
                Button(action: {
                    sourceType = .photoLibrary
                    showingImagePicker = true
                }) {
                    HStack {
                        Image(systemName: "photo.on.rectangle.angled")
                            .font(.title2)
                        Text("传统相册选择")
                            .font(.headline)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.indigo)
                    .cornerRadius(12)
                }

                // Camera Button (iOS only)
                if UIImagePickerController.isSourceTypeAvailable(.camera) {
                    Button(action: {
                        sourceType = .camera
                        showingCamera = true
                    }) {
                        HStack {
                            Image(systemName: "camera")
                                .font(.title2)
                            Text("拍照")
                                .font(.headline)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(12)
                    }
                }
                #endif
            }
            .padding(.horizontal)
        }
        .sheet(isPresented: $showingModernPicker) {
            ModernImagePicker(selectedImage: $selectedImage)
        }
        #if canImport(UIKit)
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(selectedImage: $selectedImage, sourceType: sourceType)
        }
        .sheet(isPresented: $showingCamera) {
            ImagePicker(selectedImage: $selectedImage, sourceType: .camera)
        }
        #endif
    }
}
