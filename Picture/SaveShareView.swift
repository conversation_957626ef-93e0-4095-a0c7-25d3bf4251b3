//
//  SaveShareView.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import SwiftUI
import Photos

#if canImport(UIKit)
import UIKit
typealias PlatformImage = UIImage
#elseif canImport(AppKit)
import AppKit
typealias PlatformImage = NSImage
#endif

struct SaveShareView: View {
    let originalImage: PlatformImage
    let processedImage: PlatformImage
    
    @State private var showingShareSheet = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var alertTitle = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Before/After Comparison
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 20) {
                        // Original Image
                        VStack {
                            Text("原图")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            Image(uiImage: originalImage)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 150, height: 200)
                                .cornerRadius(12)
                                .shadow(radius: 5)
                        }
                        
                        // Processed Image
                        VStack {
                            Text("处理后")
                                .font(.headline)
                                .foregroundColor(.blue)
                            
                            Image(uiImage: processedImage)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 150, height: 200)
                                .cornerRadius(12)
                                .shadow(radius: 5)
                        }
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 15) {
                    // Save to Photos
                    Button(action: saveToPhotos) {
                        HStack {
                            Image(systemName: "square.and.arrow.down")
                                .font(.title2)
                            Text("保存到相册")
                                .font(.headline)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                    
                    // Share
                    Button(action: shareImage) {
                        HStack {
                            Image(systemName: "square.and.arrow.up")
                                .font(.title2)
                            Text("分享")
                                .font(.headline)
                        }
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(12)
                    }
                    
                    // Export Options
                    HStack(spacing: 15) {
                        Button("PNG格式") {
                            exportImage(format: .png)
                        }
                        .buttonStyle(SecondaryButtonStyle())
                        
                        Button("JPEG格式") {
                            exportImage(format: .jpeg)
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("保存与分享")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("返回") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [processedImage])
        }
        .alert(alertTitle, isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - Actions
    private func saveToPhotos() {
        // Check photo library permission
        let status = PHPhotoLibrary.authorizationStatus()
        
        switch status {
        case .authorized, .limited:
            performSave()
        case .denied, .restricted:
            showPermissionAlert()
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { newStatus in
                DispatchQueue.main.async {
                    if newStatus == .authorized || newStatus == .limited {
                        self.performSave()
                    } else {
                        self.showPermissionAlert()
                    }
                }
            }
        @unknown default:
            showPermissionAlert()
        }
    }
    
    private func performSave() {
        UIImageWriteToSavedPhotosAlbum(processedImage, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        DispatchQueue.main.async {
            if let error = error {
                self.alertTitle = "保存失败"
                self.alertMessage = error.localizedDescription
            } else {
                self.alertTitle = "保存成功"
                self.alertMessage = "图片已保存到相册"
            }
            self.showingAlert = true
        }
    }
    
    private func showPermissionAlert() {
        alertTitle = "需要权限"
        alertMessage = "请在设置中允许访问相册以保存图片"
        showingAlert = true
    }
    
    private func shareImage() {
        showingShareSheet = true
    }
    
    private func exportImage(format: ImageFormat) {
        let fileName = "cutout_\(Date().timeIntervalSince1970)"
        
        switch format {
        case .png:
            if let data = processedImage.pngData() {
                saveToFiles(data: data, fileName: "\(fileName).png")
            }
        case .jpeg:
            if let data = processedImage.jpegData(compressionQuality: 0.9) {
                saveToFiles(data: data, fileName: "\(fileName).jpg")
            }
        }
    }
    
    private func saveToFiles(data: Data, fileName: String) {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            try data.write(to: fileURL)
            alertTitle = "导出成功"
            alertMessage = "文件已保存到: \(fileName)"
            showingAlert = true
        } catch {
            alertTitle = "导出失败"
            alertMessage = error.localizedDescription
            showingAlert = true
        }
    }
}

// MARK: - Supporting Types
enum ImageFormat {
    case png, jpeg
}

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Preview
struct SaveShareView_Previews: PreviewProvider {
    static var previews: some View {
        SaveShareView(
            originalImage: UIImage(systemName: "photo") ?? UIImage(),
            processedImage: UIImage(systemName: "photo.fill") ?? UIImage()
        )
    }
}
