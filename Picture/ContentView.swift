//
//  ContentView.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import SwiftUI

#if canImport(UIKit)
import UIKit
typealias PlatformImage = UIImage
#elseif canImport(AppKit)
import AppKit
typealias PlatformImage = NSImage
#endif

struct ContentView: View {
    @State private var selectedImage: PlatformImage?
    @State private var showingImagePicker = false
    @State private var showingEditingView = false

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // App Header
                VStack(spacing: 10) {
                    Image(systemName: "scissors.badge.ellipsis")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("智能抠图")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("AI驱动的专业抠图工具")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 50)

                Spacer()

                // Main Content
                if let image = selectedImage {
                    // Selected Image Preview
                    VStack(spacing: 20) {
                        #if canImport(UIKit)
                        Image(uiImage: image)
                        #elseif canImport(AppKit)
                        Image(nsImage: image)
                        #endif
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .cornerRadius(15)
                            .shadow(radius: 10)

                        Button("开始编辑") {
                            showingEditingView = true
                        }
                        .buttonStyle(PrimaryButtonStyle())

                        Button("重新选择") {
                            selectedImage = nil
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }
                } else {
                    // Image Selection
                    VStack(spacing: 20) {
                        Image(systemName: "photo.badge.plus")
                            .font(.system(size: 80))
                            .foregroundColor(.gray)

                        Text("选择一张图片开始抠图")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        ImageSourcePicker(selectedImage: $selectedImage)
                    }
                }

                Spacer()

                // Features List
                VStack(alignment: .leading, spacing: 10) {
                    FeatureRow(icon: "wand.and.stars", title: "AI智能抠图", description: "自动识别主体，一键去除背景")
                    FeatureRow(icon: "scribble", title: "手动精修", description: "精确控制选区，完美抠图效果")
                    FeatureRow(icon: "paintbrush", title: "背景替换", description: "添加纯色或图片背景")
                    FeatureRow(icon: "square.and.arrow.up", title: "便捷分享", description: "多格式导出，一键分享")
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("")
            .navigationBarHidden(true)
        }
        .fullScreenCover(isPresented: $showingEditingView) {
            if let image = selectedImage {
                EditingView(image: image)
            }
        }
    }
}

// MARK: - Supporting Views
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

#Preview {
    ContentView()
}
