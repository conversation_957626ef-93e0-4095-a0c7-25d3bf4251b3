//
//  ManualEditingOverlay.swift
//  Picture
//
//  Created by lincoo on 6/17/25.
//

import SwiftUI

struct ManualEditingOverlay: View {
    @Binding var drawingPath: [CGPoint]
    @Binding var isDrawing: Bool
    let imageSize: CGSize
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Drawing canvas
                Canvas { context, size in
                    // Draw the path
                    if drawingPath.count > 1 {
                        var path = Path()
                        path.move(to: drawingPath[0])
                        
                        for point in drawingPath.dropFirst() {
                            path.addLine(to: point)
                        }
                        
                        // Close the path if not currently drawing
                        if !isDrawing && drawingPath.count > 2 {
                            path.closeSubpath()
                        }
                        
                        // Draw stroke
                        context.stroke(
                            path,
                            with: .color(.blue),
                            style: StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round)
                        )
                        
                        // Draw fill if path is closed
                        if !isDrawing && drawingPath.count > 2 {
                            context.fill(path, with: .color(.blue.opacity(0.2)))
                        }
                    }
                    
                    // Draw points
                    for point in drawingPath {
                        context.fill(
                            Path(ellipseIn: CGRect(
                                x: point.x - 3,
                                y: point.y - 3,
                                width: 6,
                                height: 6
                            )),
                            with: .color(.blue)
                        )
                    }
                }
                .gesture(
                    DragGesture(minimumDistance: 0)
                        .onChanged { value in
                            let point = value.location
                            
                            if !isDrawing {
                                isDrawing = true
                                drawingPath.append(point)
                            } else {
                                // Add point if it's far enough from the last point
                                if let lastPoint = drawingPath.last {
                                    let distance = sqrt(
                                        pow(point.x - lastPoint.x, 2) + pow(point.y - lastPoint.y, 2)
                                    )
                                    if distance > 5 {
                                        drawingPath.append(point)
                                    }
                                }
                            }
                        }
                        .onEnded { _ in
                            isDrawing = false
                        }
                )
                
                // Instructions overlay
                if drawingPath.isEmpty {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            VStack(spacing: 10) {
                                Image(systemName: "hand.draw")
                                    .font(.title)
                                    .foregroundColor(.blue)
                                Text("绘制选区")
                                    .font(.headline)
                                    .foregroundColor(.blue)
                                Text("用手指绘制要保留的区域")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                            .padding()
                            .background(Color.white.opacity(0.9))
                            .cornerRadius(12)
                            .shadow(radius: 5)
                            Spacer()
                        }
                        Spacer()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct ManualEditingOverlay_Previews: PreviewProvider {
    static var previews: some View {
        ManualEditingOverlay(
            drawingPath: .constant([]),
            isDrawing: .constant(false),
            imageSize: CGSize(width: 300, height: 400)
        )
        .frame(width: 300, height: 400)
        .background(Color.gray.opacity(0.3))
    }
}
